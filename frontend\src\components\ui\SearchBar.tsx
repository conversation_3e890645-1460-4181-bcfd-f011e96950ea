// frontend/src/components/ui/SearchBar.tsx
import React, { useState, useRef, useEffect } from 'react';
import { Icon } from './Icon';

/**
 * Search result item interface
 */
export interface SearchResult {
  id: string;
  type: 'user' | 'conversation' | 'message';
  title: string;
  subtitle?: string;
  avatar?: string;
  timestamp?: Date;
  highlight?: string;
}

/**
 * Props for the SearchBar component
 */
export interface SearchBarProps {
  /** Placeholder text for the search input */
  placeholder?: string;
  /** Search results to display */
  results?: SearchResult[];
  /** Whether the search is currently loading */
  loading?: boolean;
  /** Callback when search query changes */
  onSearch?: (query: string) => void;
  /** Callback when a search result is selected */
  onResultSelect?: (result: SearchResult) => void;
  /** Callback when search is cleared */
  onClear?: () => void;
  /** Whether to show recent searches */
  showRecentSearches?: boolean;
  /** Recent search queries */
  recentSearches?: string[];
  /** Callback when a recent search is selected */
  onRecentSearchSelect?: (query: string) => void;
  /** Custom class name */
  className?: string;
  /** Whether the search bar is focused */
  autoFocus?: boolean;
  /** Debounce delay in milliseconds */
  debounceMs?: number;
}

/**
 * Enhanced SearchBar component for universal search functionality
 * 
 * Features:
 * - Real-time search with debouncing
 * - Search results dropdown with different result types
 * - Recent searches support
 * - Keyboard navigation (arrow keys, enter, escape)
 * - Loading states and empty states
 * - Glassmorphism design integration
 * - Accessibility support
 * 
 * @example
 * ```tsx
 * <SearchBar
 *   placeholder="Search conversations, users, or messages..."
 *   results={searchResults}
 *   loading={isSearching}
 *   onSearch={handleSearch}
 *   onResultSelect={handleResultSelect}
 *   showRecentSearches
 *   recentSearches={recentQueries}
 * />
 * ```
 */
export const SearchBar: React.FC<SearchBarProps> = ({
  placeholder = 'Search...',
  results = [],
  loading = false,
  onSearch,
  onResultSelect,
  onClear,
  showRecentSearches = false,
  recentSearches = [],
  onRecentSearchSelect,
  className = '',
  autoFocus = false,
  debounceMs = 300,
}) => {
  const [query, setQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);
  const resultsRef = useRef<HTMLDivElement>(null);
  const debounceRef = useRef<NodeJS.Timeout>();

  // Handle search with debouncing
  useEffect(() => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    debounceRef.current = setTimeout(() => {
      if (query.trim() && onSearch) {
        onSearch(query.trim());
      }
    }, debounceMs);

    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, [query, onSearch, debounceMs]);

  // Auto focus
  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newQuery = e.target.value;
    setQuery(newQuery);
    setSelectedIndex(-1);
    setIsOpen(newQuery.length > 0 || showRecentSearches);
  };

  // Handle input focus
  const handleInputFocus = () => {
    setIsOpen(query.length > 0 || showRecentSearches);
  };

  // Handle input blur
  const handleInputBlur = () => {
    // Delay closing to allow result clicks
    setTimeout(() => setIsOpen(false), 150);
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) return;

    const totalItems = query.length > 0 ? results.length : recentSearches.length;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => (prev < totalItems - 1 ? prev + 1 : prev));
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => (prev > 0 ? prev - 1 : -1));
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0) {
          if (query.length > 0 && results[selectedIndex]) {
            onResultSelect?.(results[selectedIndex]);
          } else if (query.length === 0 && recentSearches[selectedIndex]) {
            onRecentSearchSelect?.(recentSearches[selectedIndex]);
          }
        }
        setIsOpen(false);
        break;
      case 'Escape':
        setIsOpen(false);
        inputRef.current?.blur();
        break;
    }
  };

  // Handle clear
  const handleClear = () => {
    setQuery('');
    setSelectedIndex(-1);
    setIsOpen(showRecentSearches);
    onClear?.();
    inputRef.current?.focus();
  };

  // Handle result click
  const handleResultClick = (result: SearchResult) => {
    onResultSelect?.(result);
    setIsOpen(false);
  };

  // Handle recent search click
  const handleRecentSearchClick = (recentQuery: string) => {
    setQuery(recentQuery);
    onRecentSearchSelect?.(recentQuery);
    setIsOpen(false);
  };

  // Get result icon based on type
  const getResultIcon = (type: SearchResult['type']) => {
    switch (type) {
      case 'user':
        return 'user';
      case 'conversation':
        return 'message-circle';
      case 'message':
        return 'message';
      default:
        return 'search';
    }
  };

  // Format timestamp
  const formatTimestamp = (date: Date): string => {
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  const containerClasses = [
    'relative w-full',
    className,
  ].filter(Boolean).join(' ');

  const inputClasses = [
    'w-full glass border border-border-glass rounded-lg px-4 py-3 pl-10 pr-10',
    'text-text-primary placeholder-text-tertiary',
    'focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary',
    'transition-all duration-200',
  ].join(' ');

  const dropdownClasses = [
    'absolute top-full left-0 right-0 mt-2 z-50',
    'glass border border-border-glass rounded-lg shadow-lg',
    'max-h-80 overflow-y-auto',
  ].join(' ');

  return (
    <div className={containerClasses}>
      {/* Search Input */}
      <div className="relative">
        <Icon 
          name="search" 
          className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-tertiary" 
          size={18} 
        />
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          onBlur={handleInputBlur}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className={inputClasses}
        />
        {(query.length > 0 || loading) && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            {loading ? (
              <Icon name="loader" className="text-text-tertiary animate-spin" size={18} />
            ) : (
              <button
                onClick={handleClear}
                className="text-text-tertiary hover:text-text-secondary transition-colors"
              >
                <Icon name="x" size={18} />
              </button>
            )}
          </div>
        )}
      </div>

      {/* Search Results Dropdown */}
      {isOpen && (
        <div ref={resultsRef} className={dropdownClasses}>
          {query.length > 0 ? (
            /* Search Results */
            <div className="py-2">
              {results.length > 0 ? (
                results.map((result, index) => (
                  <button
                    key={result.id}
                    onClick={() => handleResultClick(result)}
                    className={`w-full px-4 py-3 text-left hover:bg-bg-glass-heavy transition-colors ${
                      selectedIndex === index ? 'bg-bg-glass-heavy' : ''
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <Icon 
                        name={getResultIcon(result.type)} 
                        className="text-text-secondary flex-shrink-0" 
                        size={16} 
                      />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-text-primary truncate">
                          {result.title}
                        </p>
                        {result.subtitle && (
                          <p className="text-xs text-text-secondary truncate">
                            {result.subtitle}
                          </p>
                        )}
                      </div>
                      {result.timestamp && (
                        <span className="text-xs text-text-tertiary flex-shrink-0">
                          {formatTimestamp(result.timestamp)}
                        </span>
                      )}
                    </div>
                  </button>
                ))
              ) : (
                <div className="px-4 py-6 text-center">
                  <Icon name="search" className="mx-auto text-text-tertiary mb-2" size={24} />
                  <p className="text-sm text-text-secondary">No results found</p>
                  <p className="text-xs text-text-tertiary mt-1">
                    Try searching for users, conversations, or messages
                  </p>
                </div>
              )}
            </div>
          ) : showRecentSearches && recentSearches.length > 0 ? (
            /* Recent Searches */
            <div className="py-2">
              <div className="px-4 py-2 border-b border-border-glass">
                <p className="text-xs font-medium text-text-secondary uppercase tracking-wide">
                  Recent Searches
                </p>
              </div>
              {recentSearches.map((recentQuery, index) => (
                <button
                  key={index}
                  onClick={() => handleRecentSearchClick(recentQuery)}
                  className={`w-full px-4 py-3 text-left hover:bg-bg-glass-heavy transition-colors ${
                    selectedIndex === index ? 'bg-bg-glass-heavy' : ''
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <Icon name="clock" className="text-text-tertiary flex-shrink-0" size={16} />
                    <span className="text-sm text-text-primary">{recentQuery}</span>
                  </div>
                </button>
              ))}
            </div>
          ) : null}
        </div>
      )}
    </div>
  );
};

export default SearchBar;
