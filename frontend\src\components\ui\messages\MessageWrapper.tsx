// frontend/src/components/ui/messages/MessageWrapper.tsx
import React, { useState } from 'react';
import { Icon } from '../Icon';
import { ProfilePhotoAvatar } from '../ProfilePhotoAvatar';

/**
 * Message status types
 */
export type MessageStatus = 'sending' | 'sent' | 'delivered' | 'read' | 'failed';

/**
 * Message reaction
 */
export interface MessageReaction {
  emoji: string;
  count: number;
  users: string[];
  hasReacted: boolean;
}

/**
 * Props for the MessageWrapper component
 */
export interface MessageWrapperProps {
  /** Message content */
  children: React.ReactNode;
  /** Message ID */
  messageId: string;
  /** Whether this message is from the current user */
  isOwn?: boolean;
  /** Sender information */
  sender?: {
    id: string;
    name: string;
    avatar?: string;
  };
  /** Message timestamp */
  timestamp: Date;
  /** Message status */
  status?: MessageStatus;
  /** Whether to show avatar */
  showAvatar?: boolean;
  /** Whether to show timestamp */
  showTimestamp?: boolean;
  /** Whether this message is part of a group */
  isGrouped?: boolean;
  /** Whether this is the first message in a group */
  isFirstInGroup?: boolean;
  /** Whether this is the last message in a group */
  isLastInGroup?: boolean;
  /** Message reactions */
  reactions?: MessageReaction[];
  /** Whether the message is selected */
  isSelected?: boolean;
  /** Whether the message is being edited */
  isEditing?: boolean;
  /** Custom class name */
  className?: string;
  /** Click handler */
  onClick?: () => void;
  /** Long press handler for mobile */
  onLongPress?: () => void;
  /** Reaction handler */
  onReaction?: (emoji: string) => void;
  /** Reply handler */
  onReply?: () => void;
  /** Edit handler */
  onEdit?: () => void;
  /** Delete handler */
  onDelete?: () => void;
  /** Copy handler */
  onCopy?: () => void;
}

/**
 * Enhanced MessageWrapper component for all message types
 * 
 * Features:
 * - Message grouping and spacing
 * - Avatar display for non-current users
 * - Timestamp and status indicators
 * - Message reactions
 * - Context menu with actions
 * - Selection states
 * - Glassmorphism design system integration
 * 
 * @example
 * ```tsx
 * <MessageWrapper
 *   messageId="msg-123"
 *   isOwn={false}
 *   sender={{ id: "user-1", name: "John Doe", avatar: "/avatar.jpg" }}
 *   timestamp={new Date()}
 *   status="read"
 *   reactions={[{ emoji: "👍", count: 2, users: ["user-2", "user-3"], hasReacted: false }]}
 *   onReaction={(emoji) => handleReaction(emoji)}
 * >
 *   <TextMessage content="Hello world!" />
 * </MessageWrapper>
 * ```
 */
export const MessageWrapper: React.FC<MessageWrapperProps> = ({
  children,
  messageId,
  isOwn = false,
  sender,
  timestamp,
  status = 'sent',
  showAvatar = true,
  showTimestamp = true,
  isGrouped = false,
  isFirstInGroup = true,
  isLastInGroup = true,
  reactions = [],
  isSelected = false,
  isEditing = false,
  className = '',
  onClick,
  onLongPress,
  onReaction,
  onReply,
  onEdit,
  onDelete,
  onCopy,
}) => {
  const [showActions, setShowActions] = useState(false);

  // Format timestamp
  const formatTimestamp = (date: Date): string => {
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString([], { weekday: 'short', hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' });
    }
  };

  // Get status icon
  const getStatusIcon = (): string | null => {
    if (!isOwn) return null;
    
    switch (status) {
      case 'sending':
        return 'clock';
      case 'sent':
        return 'check';
      case 'delivered':
        return 'checkCheck';
      case 'read':
        return 'checkCheck';
      case 'failed':
        return 'alertCircle';
      default:
        return null;
    }
  };

  // Get status color
  const getStatusColor = (): string => {
    switch (status) {
      case 'sending':
        return 'text-text-tertiary';
      case 'sent':
        return 'text-text-tertiary';
      case 'delivered':
        return 'text-text-secondary';
      case 'read':
        return 'text-primary';
      case 'failed':
        return 'text-danger';
      default:
        return 'text-text-tertiary';
    }
  };

  const statusIcon = getStatusIcon();
  const statusColor = getStatusColor();

  // Container classes
  const containerClasses = [
    'group relative',
    isOwn ? 'flex justify-end' : 'flex justify-start',
    isGrouped ? (isFirstInGroup ? 'mt-4' : 'mt-1') : 'my-2',
    isSelected ? 'bg-primary/5 rounded-lg p-2' : '',
    className,
  ].filter(Boolean).join(' ');

  // Message classes
  const messageClasses = [
    'relative max-w-xs sm:max-w-md lg:max-w-lg',
    isOwn ? 'ml-12' : 'mr-12',
  ].filter(Boolean).join(' ');

  return (
    <div 
      className={containerClasses}
      onClick={onClick}
      onContextMenu={(e) => {
        e.preventDefault();
        setShowActions(!showActions);
      }}
    >
      {/* Avatar (for non-own messages) */}
      {!isOwn && showAvatar && isLastInGroup && sender && (
        <div className="flex-shrink-0 mr-3">
          <ProfilePhotoAvatar
            src={sender.avatar}
            name={sender.name}
            size="small"
          />
        </div>
      )}

      {/* Spacer for grouped messages without avatar */}
      {!isOwn && showAvatar && (!isLastInGroup || !sender) && (
        <div className="flex-shrink-0 w-8 mr-3" />
      )}

      {/* Message content */}
      <div className={messageClasses}>
        {/* Sender name (for non-own messages in groups) */}
        {!isOwn && sender && isFirstInGroup && (
          <p className="text-xs text-text-secondary mb-1 ml-3">
            {sender.name}
          </p>
        )}

        {/* Message bubble */}
        <div className={isEditing ? 'ring-2 ring-primary/50 rounded-lg' : ''}>
          {children}
        </div>

        {/* Reactions */}
        {reactions.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-1 ml-3">
            {reactions.map((reaction, index) => (
              <button
                key={index}
                onClick={() => onReaction?.(reaction.emoji)}
                className={`inline-flex items-center px-2 py-1 rounded-full text-xs transition-colors ${
                  reaction.hasReacted
                    ? 'bg-primary/20 text-primary border border-primary/30'
                    : 'bg-bg-glass hover:bg-bg-glass-heavy border border-border-glass'
                }`}
              >
                <span className="mr-1">{reaction.emoji}</span>
                <span>{reaction.count}</span>
              </button>
            ))}
          </div>
        )}

        {/* Timestamp and status */}
        {(showTimestamp || statusIcon) && (
          <div className={`flex items-center justify-between mt-1 px-3 ${
            isOwn ? 'flex-row-reverse' : 'flex-row'
          }`}>
            {showTimestamp && (
              <span className="text-xs text-text-tertiary">
                {formatTimestamp(timestamp)}
              </span>
            )}
            
            {statusIcon && (
              <Icon 
                name={statusIcon} 
                className={`${statusColor} ${showTimestamp ? 'ml-2' : ''}`} 
                size={12} 
              />
            )}
          </div>
        )}

        {/* Action menu */}
        {showActions && (
          <div className={`absolute top-0 z-10 glass border border-border-glass rounded-lg shadow-lg p-1 ${
            isOwn ? 'right-0' : 'left-0'
          }`}>
            <div className="flex space-x-1">
              {onReaction && (
                <button
                  onClick={() => onReaction('👍')}
                  className="p-1 rounded hover:bg-bg-glass-heavy transition-colors"
                  title="React"
                >
                  <span className="text-sm">👍</span>
                </button>
              )}
              
              {onReply && (
                <button
                  onClick={onReply}
                  className="p-1 rounded hover:bg-bg-glass-heavy transition-colors"
                  title="Reply"
                >
                  <Icon name="reply" size={14} className="text-text-secondary" />
                </button>
              )}
              
              {onCopy && (
                <button
                  onClick={onCopy}
                  className="p-1 rounded hover:bg-bg-glass-heavy transition-colors"
                  title="Copy"
                >
                  <Icon name="copy" size={14} className="text-text-secondary" />
                </button>
              )}
              
              {isOwn && onEdit && (
                <button
                  onClick={onEdit}
                  className="p-1 rounded hover:bg-bg-glass-heavy transition-colors"
                  title="Edit"
                >
                  <Icon name="edit" size={14} className="text-text-secondary" />
                </button>
              )}
              
              {isOwn && onDelete && (
                <button
                  onClick={onDelete}
                  className="p-1 rounded hover:bg-bg-glass-heavy transition-colors"
                  title="Delete"
                >
                  <Icon name="trash" size={14} className="text-danger" />
                </button>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Click outside to close actions */}
      {showActions && (
        <div 
          className="fixed inset-0 z-0" 
          onClick={() => setShowActions(false)}
        />
      )}
    </div>
  );
};

export default MessageWrapper;
