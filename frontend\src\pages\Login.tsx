// frontend/src/pages/Login.tsx
import React, { useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Form, Input, Button, Icon } from '../components/ui';
import { useTheme } from '../contexts/ThemeContext';
import { useLoginMutation } from '../services/authApi';
import { loginSchema } from '../utils/validationSchemas';

interface LoginForm {
  email: string;
  password: string;
}

const Login: React.FC = () => {
  const { effectiveTheme, toggleTheme } = useTheme();
  const [loginMutation, { isLoading, error, isSuccess, data }] = useLoginMutation();
  const navigate = useNavigate();

  // Handle successful login
  useEffect(() => {
    if (isSuccess && data?.user) {
      // Tokens are already stored by the API transformation in authApi.ts
      // Navigate to dashboard
      navigate('/dashboard');
    }
  }, [isSuccess, data, navigate]);

  const handleLoginSubmit = async (values: LoginForm) => {
    // RTK Query handles the mutation automatically
    await loginMutation(values);
  };

  return (
    <div className="min-h-screen bg-bg-secondary flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        {/* Header */}
        <div className="flex justify-center">
          <div className="w-12 h-12 bg-gradient-primary rounded-lg flex items-center justify-center shadow-lg">
            <Icon name="message" className="text-white" size={24} />
          </div>
        </div>
        <h2 className="mt-6 text-center text-3xl font-bold tracking-tight text-text-primary">
          Sign in to your account
        </h2>
        <p className="mt-2 text-center text-sm text-text-secondary">
          Don't have an account?{' '}
          <Link
            to="/register"
            className="font-medium text-primary hover:text-primary-hover transition-colors"
          >
            Sign up
          </Link>
        </p>

        {/* Theme Toggle */}
        <div className="flex justify-center mt-4">
          <Button
            variant="ghost"
            icon={effectiveTheme === 'dark' ? 'sun' : 'moon'}
            onClick={toggleTheme}
            size="small"
          >
            {effectiveTheme === 'dark' ? 'Light' : 'Dark'} Mode
          </Button>
        </div>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="glass rounded-xl shadow-xl p-8">
          {/* Login Form */}
          <Form
            initialValues={{
              email: '',
              password: '',
            }}
            validationSchema={loginSchema}
            onSubmit={handleLoginSubmit}
            submitButtonText="Sign in"
            loading={isLoading}
            errorComponent={() => (
              <div className="rounded-lg bg-danger/10 border border-danger/20 p-4">
                <div className="flex items-start">
                  <Icon name="alert" className="text-danger mt-0.5 flex-shrink-0" size={16} />
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-danger">
                      Login Error
                    </h3>
                    <div className="mt-1 text-sm text-danger/80">
                      <p>{(error as any)?.data?.error || 'Login failed. Please check your credentials and try again.'}</p>
                    </div>
                  </div>
                </div>
              </div>
            )}
            showFormErrors={!!error}
          >
            {/* Email Field */}
            <Input
              name="email"
              type="email"
              label="Email Address"
              placeholder="Enter your email address"
              required
              data-testid="email-input"
            />

            {/* Password Field */}
            <div className="space-y-1">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-text-primary">Password</span>
                <Link
                  to="/forgot-password"
                  className="text-sm text-primary hover:text-primary-hover transition-colors"
                >
                  Forgot password?
                </Link>
              </div>
              <Input
                name="password"
                type="password"
                placeholder="Enter your password"
                required
                data-testid="password-input"
              />
            </div>
          </Form>

          {/* Divider */}
          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-border-glass" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-bg-glass text-text-secondary">Or continue with</span>
              </div>
            </div>
          </div>

          {/* Social Login Buttons */}
          <div className="mt-6">
            <Button
              variant="outline"
              fullWidth
              className="justify-center"
            >
              <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                <path
                  fill="currentColor"
                  d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                />
                <path
                  fill="currentColor"
                  d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                />
                <path
                  fill="currentColor"
                  d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                />
                <path
                  fill="currentColor"
                  d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                />
              </svg>
              Continue with Google
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
