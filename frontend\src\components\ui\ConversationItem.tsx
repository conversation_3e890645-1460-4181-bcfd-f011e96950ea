// frontend/src/components/ui/ConversationItem.tsx
import React from 'react';
import { ProfilePhotoAvatar } from './ProfilePhotoAvatar';
import { Icon } from './Icon';

/**
 * Conversation participant interface
 */
export interface ConversationParticipant {
  id: string;
  name: string;
  avatar?: string;
  status?: 'online' | 'offline' | 'away' | 'busy';
}

/**
 * Last message interface
 */
export interface LastMessage {
  id: string;
  content: string;
  type: 'text' | 'image' | 'file' | 'audio' | 'system';
  senderId: string;
  senderName: string;
  timestamp: Date;
  isOwn: boolean;
}

/**
 * Props for the ConversationItem component
 */
export interface ConversationItemProps {
  /** Conversation ID */
  id: string;
  /** Conversation name/title */
  name: string;
  /** Whether this is a group conversation */
  isGroup?: boolean;
  /** Conversation participants */
  participants: ConversationParticipant[];
  /** Last message in the conversation */
  lastMessage?: LastMessage;
  /** Number of unread messages */
  unreadCount?: number;
  /** Whether the conversation is currently selected */
  isSelected?: boolean;
  /** Whether the conversation is muted */
  isMuted?: boolean;
  /** Whether the conversation is pinned */
  isPinned?: boolean;
  /** Whether someone is typing */
  isTyping?: boolean;
  /** Names of users who are typing */
  typingUsers?: string[];
  /** Conversation avatar (for groups) */
  avatar?: string;
  /** Whether the conversation is archived */
  isArchived?: boolean;
  /** Custom class name */
  className?: string;
  /** Click handler */
  onClick?: () => void;
  /** Context menu handler */
  onContextMenu?: (e: React.MouseEvent) => void;
  /** Long press handler for mobile */
  onLongPress?: () => void;
}

/**
 * Enhanced ConversationItem component for conversation lists
 * 
 * Features:
 * - Support for both individual and group conversations
 * - Unread message count badges
 * - Typing indicators
 * - Online status indicators
 * - Muted and pinned conversation indicators
 * - Last message preview with different message types
 * - Glassmorphism design integration
 * - Responsive design for mobile and desktop
 * 
 * @example
 * ```tsx
 * <ConversationItem
 *   id="conv-123"
 *   name="John Doe"
 *   participants={[{ id: "user-1", name: "John Doe", status: "online" }]}
 *   lastMessage={{
 *     content: "Hey, how are you?",
 *     type: "text",
 *     timestamp: new Date(),
 *     isOwn: false
 *   }}
 *   unreadCount={3}
 *   onClick={() => selectConversation("conv-123")}
 * />
 * ```
 */
export const ConversationItem: React.FC<ConversationItemProps> = ({
  id,
  name,
  isGroup = false,
  participants,
  lastMessage,
  unreadCount = 0,
  isSelected = false,
  isMuted = false,
  isPinned = false,
  isTyping = false,
  typingUsers = [],
  avatar,
  isArchived = false,
  className = '',
  onClick,
  onContextMenu,
  onLongPress,
}) => {
  // Get the primary participant for individual conversations
  const primaryParticipant = !isGroup && participants.length > 0 ? participants[0] : null;

  // Format last message content based on type
  const formatLastMessage = (message: LastMessage): string => {
    switch (message.type) {
      case 'image':
        return '📷 Photo';
      case 'file':
        return '📎 File';
      case 'audio':
        return '🎵 Audio';
      case 'system':
        return message.content;
      default:
        return message.content;
    }
  };

  // Format timestamp
  const formatTimestamp = (date: Date): string => {
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return 'now';
    } else if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  // Get typing indicator text
  const getTypingText = (): string => {
    if (!isTyping || typingUsers.length === 0) return '';
    
    if (typingUsers.length === 1) {
      return `${typingUsers[0]} is typing...`;
    } else if (typingUsers.length === 2) {
      return `${typingUsers[0]} and ${typingUsers[1]} are typing...`;
    } else {
      return `${typingUsers.length} people are typing...`;
    }
  };

  const containerClasses = [
    'flex items-center p-4 cursor-pointer transition-all duration-200',
    'hover:bg-bg-glass-light border-b border-border-glass/50',
    isSelected ? 'bg-primary/10 border-r-2 border-r-primary' : '',
    isArchived ? 'opacity-60' : '',
    className,
  ].filter(Boolean).join(' ');

  return (
    <div
      className={containerClasses}
      onClick={onClick}
      onContextMenu={onContextMenu}
      data-conversation-id={id}
    >
      {/* Avatar */}
      <div className="flex-shrink-0 mr-3 relative">
        {isGroup ? (
          <div className="w-12 h-12 bg-gradient-primary rounded-full flex items-center justify-center">
            <Icon name="users" className="text-white" size={20} />
          </div>
        ) : (
          <ProfilePhotoAvatar
            src={primaryParticipant?.avatar || avatar}
            name={primaryParticipant?.name || name}
            size="medium"
            status={primaryParticipant?.status}
            showStatus={!isGroup}
          />
        )}
        
        {/* Pinned indicator */}
        {isPinned && (
          <div className="absolute -top-1 -right-1 w-4 h-4 bg-primary rounded-full flex items-center justify-center">
            <Icon name="pin" className="text-white" size={10} />
          </div>
        )}
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        {/* Header */}
        <div className="flex items-center justify-between mb-1">
          <div className="flex items-center space-x-2 min-w-0 flex-1">
            <h3 className="text-sm font-medium text-text-primary truncate">
              {name}
            </h3>
            {isMuted && (
              <Icon name="volume-x" className="text-text-tertiary flex-shrink-0" size={14} />
            )}
          </div>
          
          <div className="flex items-center space-x-2 flex-shrink-0">
            {lastMessage && (
              <span className="text-xs text-text-tertiary">
                {formatTimestamp(lastMessage.timestamp)}
              </span>
            )}
            {unreadCount > 0 && (
              <div className="bg-primary text-white text-xs rounded-full min-w-[18px] h-[18px] flex items-center justify-center px-1">
                {unreadCount > 99 ? '99+' : unreadCount}
              </div>
            )}
          </div>
        </div>

        {/* Last Message or Typing Indicator */}
        <div className="flex items-center justify-between">
          <div className="flex-1 min-w-0">
            {isTyping ? (
              <div className="flex items-center space-x-2">
                <div className="flex space-x-1">
                  <div className="w-1 h-1 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                  <div className="w-1 h-1 bg-primary rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                  <div className="w-1 h-1 bg-primary rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                </div>
                <span className="text-xs text-primary font-medium">
                  {getTypingText()}
                </span>
              </div>
            ) : lastMessage ? (
              <p className="text-sm text-text-secondary truncate">
                {!isGroup && lastMessage.isOwn && (
                  <span className="text-text-tertiary mr-1">You: </span>
                )}
                {isGroup && !lastMessage.isOwn && (
                  <span className="text-text-tertiary mr-1">{lastMessage.senderName}: </span>
                )}
                {formatLastMessage(lastMessage)}
              </p>
            ) : (
              <p className="text-sm text-text-tertiary italic">No messages yet</p>
            )}
          </div>
        </div>

        {/* Group participants preview (for groups) */}
        {isGroup && participants.length > 0 && (
          <div className="flex items-center mt-1 space-x-1">
            <Icon name="users" className="text-text-tertiary" size={12} />
            <span className="text-xs text-text-tertiary">
              {participants.length} member{participants.length !== 1 ? 's' : ''}
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

export default ConversationItem;
