// frontend/src/containers/MessageContainer.tsx
import React, { useEffect, useRef, useMemo, useCallback, useState } from 'react';
import { useSelector } from 'react-redux';
import { List as FixedSizeList } from "react-window";
import type { CellComponentProps as ListChildComponentProps } from "react-window";
import { useSocket } from '../contexts/SocketContext';
import { useAuth } from '../contexts/AuthContext';
import { useGetMessagesQuery } from '../services/messageApi';
import {
  MessageWrapper,
  TextMessage,
  ImageMessage,
  AudioMessage,
  FileMessage,
  GroupUpdateMessage,
  CallingMessage
} from '../components/ui/messages';
import { Button, Icon, MessageInput } from '../components/ui';
import {
  selectSortedMessagesByConversation,
  selectSendingMessages,
  selectMessageStatuses,
  selectFailedMessages,
  selectTypingUsersByConversation,
  type RootState
} from '../store';
import type { Message as ApiMessage } from '../services/messageApi';
import type { Message as StoreMessage, MessageStatusType } from '../store/slices/messageSlice';

// Union type to handle both API and store message formats
type MessageType = ApiMessage | StoreMessage;

/**
 * Helper functions to normalize message properties across different formats
 */
const getMessageTimestamp = (message: MessageType): string => {
  // Both API and Store messages should have createdAt
  return (message as any).createdAt || (message as any).created_at;
};

const getMessageSenderId = (message: MessageType): string => {
  return message.sender.id;
};

const getMessageType = (message: MessageType): string => {
  // Both API and Store messages should have messageType
  return (message as any).messageType || (message as any).message_type;
};

const getSenderName = (sender: MessageType['sender']): string => {
  const firstName = sender.first_name || '';
  const lastName = sender.last_name || '';
  return `${firstName} ${lastName}`.trim() || sender.username;
};

const getSenderAvatar = (sender: MessageType['sender']): string | undefined => {
  return sender.profile_picture;
};

/**
 * Props for the MessageContainer component
 */
export interface MessageContainerProps {
  /** Conversation ID */
  conversationId: string;
  /** Custom class name */
  className?: string;
  /** Reply handler */
  onReply?: (message: MessageType) => void;
}

/**
 * Message item height for virtualization
 */
const MESSAGE_ITEM_HEIGHT = 80;

/**
 * Props for the MessageContainer component
 */
export interface MessageContainerProps {
  /** Conversation ID */
  conversationId: string;
  /** Custom class name */
  className?: string;
  /** Reply handler */
  onReply?: (message: MessageType) => void;
}


/**
 * Enhanced MessageContainer component with virtualization and comprehensive message handling
 *
 * Features:
 * - Message virtualization for performance with large lists using react-window
 * - Real-time message updates via Socket.IO
 * - Support for all message types (text, image, audio, file, system)
 * - Message status indicators and retry functionality
 * - Typing indicators
 * - Auto-scroll to bottom for new messages
 * - Draft conversation support
 * - Connection status handling
 * - Separate MessageInput component integration
 *
 * Replaces: ChatRoom.tsx and MessageList.tsx
 */
export const MessageContainer: React.FC<MessageContainerProps> = ({
  conversationId,
  className = '',
  onReply,
}) => {
  const { user } = useAuth();
  const { joinConversation, isConnected } = useSocket();
  const listRef = useRef<typeof FixedSizeList>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [shouldAutoScroll, setShouldAutoScroll] = useState(true);
  const [replyTo, setReplyTo] = useState<{
    id: string;
    content: string;
    sender: string;
  } | null>(null);
  const [listHeight, setListHeight] = useState(400);

  // Check if this is a draft conversation
  const isDraftConversation = conversationId?.startsWith('draft-');

  // Use RTK Query to get messages - skip for draft conversations
  const { data: messagesData, isLoading, error } = useGetMessagesQuery(
    { conversationId },
    { skip: !conversationId || isDraftConversation }
  );

  // Use memoized selectors for Redux store data (real-time features)
  const sendingMessagesMap = useSelector(selectSendingMessages);
  const messageStatuses = useSelector(selectMessageStatuses);
  const failedMessagesMap = useSelector(selectFailedMessages);
  const reduxMessages = useSelector((state: RootState) =>
    selectSortedMessagesByConversation(state, conversationId)
  );
  const typingUsers = useSelector((state: RootState) =>
    selectTypingUsersByConversation(state, conversationId)
  );

  // Convert objects to arrays for easier checking
  const sendingMessages = Object.keys(sendingMessagesMap).filter(key => sendingMessagesMap[key]);
  const failedMessages = Object.keys(failedMessagesMap).filter(key => failedMessagesMap[key]);

  // Combine API messages with real-time Redux messages
  const allMessages = useMemo(() => {
    const apiMessages = messagesData?.results || [];
    const combinedMessages = [...apiMessages, ...reduxMessages];

    // Remove duplicates based on message ID
    const uniqueMessages = combinedMessages.reduce((acc, message) => {
      if (!acc.find(m => m.id === message.id)) {
        acc.push(message);
      }
      return acc;
    }, [] as MessageType[]);

    // Sort by timestamp
    return uniqueMessages.sort((a, b) =>
      new Date(getMessageTimestamp(a)).getTime() - new Date(getMessageTimestamp(b)).getTime()
    );
  }, [messagesData?.results, reduxMessages]);

  // Join conversation room for real-time updates
  useEffect(() => {
    if (conversationId && isConnected && !isDraftConversation) {
      joinConversation(conversationId);
    }
  }, [conversationId, joinConversation, isConnected, isDraftConversation]);

  // Dynamic height calculation for react-window
  useEffect(() => {
    const updateHeight = () => {
      if (messagesContainerRef.current) {
        const rect = messagesContainerRef.current.getBoundingClientRect();
        setListHeight(rect.height);
      }
    };

    updateHeight();
    
    const resizeObserver = new ResizeObserver(updateHeight);
    if (messagesContainerRef.current) {
      resizeObserver.observe(messagesContainerRef.current);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (shouldAutoScroll && allMessages.length > 0) {
      scrollToBottom();
    }
  }, [allMessages.length, shouldAutoScroll]);

  // Scroll to bottom function
  const scrollToBottom = useCallback(() => {
    if (listRef.current) {
      listRef.current.scrollTo({
        top: allMessages.length * MESSAGE_ITEM_HEIGHT,
        behavior: 'smooth'
      });
    }
  }, [allMessages.length]);

  // Render individual message item for react-window
  const renderMessageItem = useCallback(({ rowIndex: index, style }: ListChildComponentProps) => {
    const message = allMessages[index];
    if (!message || !user) return null;

    const isOwnMessage = getMessageSenderId(message) === user.id;
    const messageId = message.id;
    const isSending = sendingMessages.includes(messageId);
    const messageStatus = messageStatuses[messageId];
    const isFailed = failedMessages.includes(messageId);

    // Get sender information
    const sender = message.sender ? {
      id: message.sender.id,
      name: getSenderName(message.sender),
      avatar: getSenderAvatar(message.sender),
    } : undefined;

    // Render message content based on type
    const renderMessageContent = () => {
      const messageType = getMessageType(message);
      const messageContent = message.content;

      switch (messageType) {
        case 'TEXT':
          return (
            <TextMessage
              content={messageContent}
              isOwn={isOwnMessage}
              enableLinkDetection
            />
          );
        case 'IMAGE':
          return (
            <ImageMessage
              src={(message as any).mediaUrl || ''}
              alt="Image message"
              caption={messageContent}
              isOwn={isOwnMessage}
              enableLightbox
              showDownload
            />
          );
        case 'AUDIO':
          return (
            <AudioMessage
              src={(message as any).mediaUrl || ''}
              duration={(message as any).mediaDuration}
              isOwn={isOwnMessage}
              showWaveform
            />
          );
        case 'FILE':
          return (
            <FileMessage
              file={{
                name: (message as any).fileName || 'Unknown file',
                size: (message as any).fileSize || 0,
                type: (message as any).mimeType || 'application/octet-stream',
                url: (message as any).mediaUrl || '',
              }}
              isOwn={isOwnMessage}
              showPreview
            />
          );
        case 'SYSTEM':
          if (messageContent.includes('call')) {
            return (
              <CallingMessage
                callType={messageContent.includes('video') ? 'video' : 'audio'}
                status={messageContent.includes('missed') ? 'missed' : 'ended'}
                duration={(message as any).mediaDuration}
                isOutgoing={isOwnMessage}
              />
            );
          } else {
            return (
              <GroupUpdateMessage
                updateType="member_added" // This should be determined from message content
                content={messageContent}
                timestamp={new Date(getMessageTimestamp(message))}
              />
            );
          }
        default:
          return (
            <TextMessage
              content={messageContent}
              isOwn={isOwnMessage}
              enableLinkDetection
            />
          );
      }
    };

    return (
      <div style={style} className="px-4">
        <MessageWrapper
          key={messageId}
          isOwn={isOwnMessage}
          className="mb-2"
        >
          {renderMessageContent()}
        </MessageWrapper>
      </div>
    );
  }, [allMessages, user, sendingMessages, messageStatuses, failedMessages]);

  // Show loading state
  if (isLoading && !isDraftConversation) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <Icon name="loader" className="animate-spin text-text-secondary mb-2" size={24} />
          <p className="text-text-secondary">Loading messages...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error && !isDraftConversation) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <Icon name="alert-circle" className="text-danger mb-2" size={24} />
          <p className="text-danger">Failed to load messages</p>
          <Button variant="outline" size="small" className="mt-2">
            Retry
          </Button>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <p className="text-text-secondary">Please log in to access chat</p>
      </div>
    );
  }

  const containerClasses = [
    'flex flex-col h-full bg-bg-primary',
    className,
  ].filter(Boolean).join(' ');

  return (
    <div className={containerClasses}>
      {/* Connection Status */}
      {!isConnected && (
        <div className="bg-warning/10 border-b border-warning/20 px-4 py-2">
          <div className="flex items-center space-x-2 text-warning text-sm">
            <div className="w-2 h-2 bg-warning rounded-full animate-pulse"></div>
            <span>Reconnecting...</span>
          </div>
        </div>
      )}

      {/* Messages List with Virtualization */}
      <div className="flex-1 relative">
        {allMessages.length > 0 ? (
          <FixedSizeList
            ref={listRef}
            height={listHeight}
            itemCount={allMessages.length}
            itemSize={MESSAGE_ITEM_HEIGHT}
            className="scrollbar-thin scrollbar-thumb-border-glass scrollbar-track-transparent"
            onScroll={({ scrollOffset, scrollUpdateWasRequested }: { scrollOffset: number; scrollUpdateWasRequested: boolean }) => {
              if (listRef.current) {
                const scrollHeight = listRef.current.getTotalSize();
                const clientHeight = listHeight;
                const isAtBottom = scrollOffset + clientHeight >= scrollHeight - 100;
                setShouldAutoScroll(isAtBottom);
              }
            }}
          >
            {renderMessageItem}
          </FixedSizeList>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <Icon name="message-circle" className="text-text-tertiary mb-3" size={32} />
              <h3 className="text-text-secondary font-medium mb-1">No messages yet</h3>
              <p className="text-text-tertiary text-sm">
                Start the conversation by sending a message
              </p>
            </div>
          </div>
        )}

        {/* Typing Indicators */}
        {typingUsers.length > 0 && (
          <div className="absolute bottom-4 left-4 bg-bg-glass rounded-lg px-3 py-2 border border-border-glass">
            <div className="flex items-center space-x-2">
              <div className="flex space-x-1">
                <div className="w-1 h-1 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                <div className="w-1 h-1 bg-primary rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                <div className="w-1 h-1 bg-primary rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
              </div>
              <span className="text-xs text-text-secondary">
                {typingUsers.length === 1
                  ? `${typingUsers[0]} is typing...`
                  : `${typingUsers.length} people are typing...`
                }
              </span>
            </div>
          </div>
        )}

        {/* Scroll to bottom button */}
        {!shouldAutoScroll && (
          <button
            onClick={scrollToBottom}
            className="absolute bottom-4 right-4 bg-primary text-white rounded-full p-2 shadow-lg hover:bg-primary-hover transition-colors"
          >
            <Icon name="arrow-down" size={16} />
          </button>
        )}
      </div>

      {/* Message Input */}
      <MessageInput
        conversationId={conversationId}
        disabled={!isConnected}
        replyTo={replyTo}
        onCancelReply={() => setReplyTo(null)}
      />
    </div>
  );
};

export default MessageContainer;
