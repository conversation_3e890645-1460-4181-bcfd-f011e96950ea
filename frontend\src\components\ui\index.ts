// frontend/src/components/ui/index.ts

// Core UI Components
export { Button, type ButtonProps, type ButtonVariant, type ButtonSize } from './Button';
export { Input, type InputProps, type InputSize, type InputType } from './Input';
export { TextField, type TextFieldProps, type TextFieldSize } from './TextField';
export { Select, type SelectProps, type SelectOption, type SelectSize } from './Select';
export { Form, FormField, FormSection, type FormProps, type FormAction } from './Form';
export { ProfilePhotoAvatar, AvatarGroup, type ProfilePhotoAvatarProps, type AvatarGroupProps, type AvatarSize, type UserStatus } from './ProfilePhotoAvatar';

// Utility Components
export { Icon, type IconProps } from './Icon';
export { ErrorBoundary } from './ErrorBoundary';
export { LoadingSpinner } from './LoadingSpinner';
export { ApiErrorDisplay } from './ApiErrorDisplay';

// Message Components
export * from './messages';

// Chat Interface Components
export { SearchBar, type SearchBarProps, type SearchResult } from './SearchBar';
export { ConversationItem, type ConversationItemProps, type ConversationParticipant, type LastMessage } from './ConversationItem';
export { Sidebar, type SidebarProps, type SidebarTab } from './Sidebar';
export { MessageInput, type MessageInputProps } from './MessageInput';

// Re-export design system constants for convenience
export { DESIGN_SYSTEM, COLORS, TYPOGRAPHY, SPACING, BORDER_RADIUS, GLASSMORPHISM, SHADOWS, GRADIENTS, COMPONENT_SIZES, ANIMATION, Z_INDEX, BREAKPOINTS, STATUS_COLORS, MESSAGE_TYPES, THEMES } from '../../constants/designSystem';
