// frontend/src/components/ui/Button.tsx
import React, { forwardRef } from 'react';
import { Icon } from './Icon';
import { Icons } from './Icon';
import { COMPONENT_SIZES, ANIMATION } from '../../constants/designSystem';

/**
 * Button component variants
 */
export type ButtonVariant = 'primary' | 'secondary' | 'danger' | 'ghost' | 'outline';

/**
 * Button component sizes
 */
export type ButtonSize = 'small' | 'medium' | 'large';

/**
 * Props for the Button component
 */
export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** Visual style variant of the button */
  variant?: ButtonVariant;
  /** Size of the button */
  size?: ButtonSize;
  /** Whether the button is in a loading state */
  loading?: boolean;
  /** Icon to display in the button */
  icon?: keyof typeof Icons;
  /** Position of the icon relative to the text */
  iconPosition?: 'left' | 'right' | 'icon-only';
  /** Whether the button should take full width of its container */
  fullWidth?: boolean;
  /** Custom loading text to display when loading */
  loadingText?: string;
  /** Custom icon size override */
  iconSize?: number;
  /** Gap between icon and text */
  iconGap?: 'xs' | 'sm' | 'md' | 'lg';
}

/**
 * Enhanced Button component with comprehensive styling and functionality
 *
 * Features:
 * - Multiple variants (primary, secondary, danger, ghost, outline)
 * - Three sizes (small, medium, large)
 * - Loading states with spinner
 * - Icon support with positioning (left, right, icon-only)
 * - Configurable icon spacing and sizing
 * - Full width option
 * - Glassmorphism design system integration
 * - Comprehensive accessibility support
 *
 * @example
 * ```tsx
 * <Button variant="primary" size="medium" loading={isLoading}>
 *   Save Changes
 * </Button>
 *
 * <Button variant="outline" icon="plus" iconPosition="left" iconGap="md">
 *   Add Item
 * </Button>
 *
 * <Button variant="ghost" icon="settings" iconPosition="icon-only" size="small">
 *   Settings
 * </Button>
 * ```
 */
export const Button = forwardRef<HTMLButtonElement, ButtonProps>(({
  children,
  variant = 'primary',
  size = 'medium',
  loading = false,
  icon,
  iconPosition = 'left',
  fullWidth = false,
  loadingText,
  iconSize: customIconSize,
  iconGap = 'sm',
  className = '',
  disabled,
  type = 'button',
  ...props
}, ref) => {
  // Base classes with glassmorphism support
  const baseClasses = [
    'inline-flex items-center justify-center',
    'font-medium transition-all duration-250',
    'focus:outline-none focus:ring-2 focus:ring-offset-2',
    'disabled:cursor-not-allowed',
    'relative overflow-hidden',
    fullWidth ? 'w-full' : '',
    iconPosition === 'icon-only' ? '!px-0' : '', // Reduce horizontal padding for icon-only buttons
  ].filter(Boolean).join(' ');

  // Variant-specific classes with glassmorphism effects
  const variantClasses = {
    primary: [
      'bg-gradient-to-r from-blue-600 to-indigo-600',
      'text-white shadow-lg',
      'hover:from-blue-700 hover:to-indigo-700 hover:shadow-xl',
      'focus:ring-blue-500',
      'disabled:from-gray-400 disabled:to-gray-500',
      'active:scale-95',
    ].join(' '),

    secondary: [
      'glass border-border-glass',
      'text-text-primary shadow-md',
      'hover:bg-bg-glass-heavy hover:shadow-lg',
      'focus:ring-primary',
      'disabled:opacity-50',
      'active:scale-95',
    ].join(' '),

    danger: [
      'bg-gradient-to-r from-red-500 to-red-600',
      'text-white shadow-lg',
      'hover:from-red-600 hover:to-red-700 hover:shadow-xl',
      'focus:ring-red-500',
      'disabled:from-gray-400 disabled:to-gray-500',
      'active:scale-95',
    ].join(' '),

    ghost: [
      'text-text-secondary',
      'hover:bg-bg-glass hover:text-text-primary',
      'focus:ring-primary',
      'disabled:opacity-50',
      'active:scale-95',
    ].join(' '),

    outline: [
      'border-2 border-border-primary',
      'text-text-primary bg-transparent',
      'hover:bg-bg-glass hover:border-border-focus',
      'focus:ring-primary focus:border-border-focus',
      'disabled:opacity-50 disabled:border-border-secondary',
      'active:scale-95',
    ].join(' '),
  };

  // Size-specific classes
  const sizeClasses = {
    small: iconPosition === 'icon-only'
      ? 'p-1.5 text-sm rounded-md min-h-[2rem] min-w-[2rem]' 
      : 'px-3 py-1.5 text-sm rounded-md min-h-[2rem]',
    medium: iconPosition === 'icon-only'
      ? 'p-2 text-sm rounded-lg min-h-[2.5rem] min-w-[2.5rem]' 
      : 'px-4 py-2 text-sm rounded-lg min-h-[2.5rem]',
    large: iconPosition === 'icon-only'
      ? 'p-3 text-base rounded-xl min-h-[3rem] min-w-[3rem]' 
      : 'px-6 py-3 text-base rounded-xl min-h-[3rem]',
  };

  // Icon size based on button size
  const iconSize = {
    small: 14,
    medium: 16,
    large: 18,
  };

  // Icon gap spacing based on iconGap prop
  const iconGapClasses = {
    xs: 'gap-1',
    sm: 'gap-2',
    md: 'gap-3',
    lg: 'gap-4',
  };

  // Determine final icon size
  const finalIconSize = customIconSize || iconSize[size];

  const isDisabled = disabled || loading;
  const displayText = loading && loadingText ? loadingText : children;
  
  // Determine if we should show text content
  const hasTextContent = Boolean(displayText) && iconPosition !== 'icon-only';
  const showIcon = icon && !loading;
  const isIconOnly = iconPosition === 'icon-only';

  return (
    <button
      ref={ref}
      type={type}
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${showIcon && hasTextContent ? iconGapClasses[iconGap] : ''} ${className}`}
      disabled={isDisabled}
      aria-label={isIconOnly && typeof children === 'string' ? children : props['aria-label']}
      {...props}
    >
      {/* Loading spinner */}
      {loading && (
        <Icon
          name="spinner"
          className={`animate-spin ${hasTextContent ? '' : ''}`}
          size={finalIconSize}
        />
      )}

      {/* Left icon */}
      {showIcon && (iconPosition === 'left' || isIconOnly) && (
        <Icon
          name={icon}
          size={finalIconSize}
        />
      )}

      {/* Button text */}
      {hasTextContent && (
        <span className={loading ? 'opacity-75' : ''}>
          {displayText}
        </span>
      )}

      {/* Right icon */}
      {showIcon && iconPosition === 'right' && !isIconOnly && (
        <Icon
          name={icon}
          size={finalIconSize}
        />
      )}
    </button>
  );
});

Button.displayName = 'Button';
