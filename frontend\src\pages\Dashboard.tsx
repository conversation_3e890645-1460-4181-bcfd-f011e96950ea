// frontend/src/pages/Dashboard.tsx
import React from 'react';
import { useSelector } from 'react-redux';
import { Button, Icon, ProfilePhotoAvatar } from '../components/ui';
import { useAuth } from '../contexts/AuthContext';
import { useSocket } from '../contexts/SocketContext';
import { useTheme } from '../contexts/ThemeContext';
import type { RootState } from '../store';
import { Sidebar } from '../components/ui';
import { ChatHeader } from '../components/Chat/ChatHeader';
import { MessageContainer } from '../containers/MessageContainer';
import UserSearch from '../components/Chat/UserSearch';

export const Dashboard: React.FC = () => {
  const { user, logout } = useAuth();
  const { isConnected } = useSocket();
  const { effectiveTheme, toggleTheme } = useTheme();
  const selectedConversationId = useSelector((state: RootState) => state.conversations.selectedConversationId);
  const [showUserSearch, setShowUserSearch] = React.useState(false);

  const handleLogout = () => {
    logout();
  };

  const handleConversationCreated = () => {
    // The conversation will be automatically selected by the Redux action
    setShowUserSearch(false);
  };

  return (
    <div className="h-screen bg-bg-secondary flex flex-col">
      {/* Header */}
      <header className="glass border-b border-border-glass" data-testid="dashboard-header">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            {/* Logo and Title */}
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-gradient-primary rounded-lg flex items-center justify-center">
                  <Icon name="message" className="text-white" size={20} />
                </div>
              </div>
              <div className="ml-3">
                <h1 className="text-xl font-bold text-text-primary">Chat App</h1>
              </div>
            </div>

            {/* Header Actions */}
            <div className="flex items-center space-x-3">
              {/* Connection status */}
              <div className={`flex items-center space-x-2 text-sm ${isConnected ? 'text-success' : 'text-warning'
                }`} data-testid="connection-status">
                <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-success' : 'bg-warning animate-pulse'
                  }`}></div>
                <span className="hidden sm:inline">
                  {isConnected ? 'Connected' : 'Connecting'}
                </span>
              </div>

              {/* Theme Toggle */}
              <Button
                variant="ghost"
                size="small"
                icon={effectiveTheme === 'dark' ? 'sun' : 'moon'}
                onClick={toggleTheme}
                title={`Switch to ${effectiveTheme === 'dark' ? 'light' : 'dark'} mode`}
              />

              {/* User Profile */}
              <div className="flex items-center space-x-3">
                <ProfilePhotoAvatar
                  src={user?.profilePicture}
                  name={`${user?.name || 'User'}`}
                  size="small"
                  status="online"
                  showStatus
                  clickable
                  title={`${user?.name || 'User'}`}
                />

                <Button
                  variant="ghost"
                  size="small"
                  onClick={handleLogout}
                  icon="logout"
                  data-testid="logout-button"
                  className="hidden sm:flex"
                >
                  Logout
                </Button>

                {/* Mobile logout */}
                <Button
                  variant="ghost"
                  size="small"
                  onClick={handleLogout}
                  icon="logout"
                  data-testid="logout-button-mobile"
                  className="sm:hidden"
                />
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main chat interface */}
      <div className="flex-1 flex overflow-hidden">
        {/* Conversation list sidebar */}
        <aside className="w-80 glass-light border-r border-border-glass flex flex-col" data-testid="conversation-list">
          <div className="p-4 border-b border-border-glass">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-text-primary">Conversations</h2>
              <Button
                onClick={() => setShowUserSearch(true)}
                size="small"
                variant="primary"
                icon="plus"
                data-testid="new-chat-button"
              >
                <Icon name="plus" size={16} />
                <span>New Chat</span>
              </Button>
            </div>
          </div>
          <div className="flex-1 overflow-hidden">
            {user && <Sidebar />}
          </div>
        </aside>

        {/* Chat area */}
        <div className="flex-1 flex flex-col" data-testid="chat-area">
          {selectedConversationId ? (
            <>
              {/* Chat Header */}
              <ChatHeader
                conversationId={selectedConversationId}
                currentUserId={user?.id || ''}
              />

              {/* Message Container */}
              <MessageContainer conversationId={selectedConversationId} />
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center bg-bg-secondary">
              <div className="text-center">
                <Icon name="message-circle" className="mx-auto text-text-tertiary" size={48} />
                <h3 className="mt-2 text-sm font-medium text-text-primary">No conversation selected</h3>
                <p className="mt-1 text-sm text-text-secondary">
                  Choose a conversation from the sidebar to start messaging
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* User Search Modal */}
      {showUserSearch && (
        <UserSearch
          onClose={() => setShowUserSearch(false)}
          onConversationCreated={handleConversationCreated}
        />
      )}
    </div>
  );
};
