// frontend/src/components/ui/Sidebar.tsx
import React, { useState } from 'react';
import { Button } from './Button';
import { SearchBar, type SearchResult } from './SearchBar';
import { ConversationItem, type ConversationItemProps } from './ConversationItem';
import { Icon } from './Icon';

/**
 * Sidebar tab types
 */
export type SidebarTab = 'conversations' | 'contacts' | 'archived';

/**
 * Props for the Sidebar component
 */
export interface SidebarProps {
  /** Current active tab */
  activeTab?: SidebarTab;
  /** List of conversations */
  conversations?: ConversationItemProps[];
  /** Currently selected conversation ID */
  selectedConversationId?: string;
  /** Search results */
  searchResults?: SearchResult[];
  /** Whether search is loading */
  searchLoading?: boolean;
  /** Recent search queries */
  recentSearches?: string[];
  /** Whether the sidebar is collapsed (mobile) */
  isCollapsed?: boolean;
  /** Custom class name */
  className?: string;
  /** Tab change handler */
  onTabChange?: (tab: SidebarTab) => void;
  /** Search handler */
  onSearch?: (query: string) => void;
  /** Search result selection handler */
  onSearchResultSelect?: (result: SearchResult) => void;
  /** Conversation selection handler */
  onConversationSelect?: (conversationId: string) => void;
  /** New conversation handler */
  onNewConversation?: () => void;
  /** Settings handler */
  onSettings?: () => void;
  /** Archive handler */
  onArchive?: () => void;
}

/**
 * Enhanced Sidebar component for chat navigation
 * 
 * Features:
 * - Tabbed interface (Conversations, Contacts, Archived)
 * - Universal search with results dropdown
 * - Conversation list with unread counts and status
 * - New conversation and settings buttons
 * - Responsive design with collapse support
 * - Glassmorphism design integration
 * - Keyboard navigation support
 * 
 * @example
 * ```tsx
 * <Sidebar
 *   activeTab="conversations"
 *   conversations={conversationList}
 *   selectedConversationId="conv-123"
 *   onTabChange={handleTabChange}
 *   onSearch={handleSearch}
 *   onConversationSelect={handleConversationSelect}
 *   onNewConversation={handleNewConversation}
 * />
 * ```
 */
export const Sidebar: React.FC<SidebarProps> = ({
  activeTab = 'conversations',
  conversations = [],
  selectedConversationId,
  searchResults = [],
  searchLoading = false,
  recentSearches = [],
  isCollapsed = false,
  className = '',
  onTabChange,
  onSearch,
  onSearchResultSelect,
  onConversationSelect,
  onNewConversation,
  onSettings,
  onArchive,
}) => {
  const [searchQuery, setSearchQuery] = useState('');

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    onSearch?.(query);
  };

  // Handle search result selection
  const handleSearchResultSelect = (result: SearchResult) => {
    setSearchQuery('');
    onSearchResultSelect?.(result);
  };

  // Handle conversation click
  const handleConversationClick = (conversation: ConversationItemProps) => {
    onConversationSelect?.(conversation.id);
  };

  // Filter conversations based on active tab
  const filteredConversations = conversations.filter(conv => {
    switch (activeTab) {
      case 'archived':
        return conv.isArchived;
      case 'conversations':
      default:
        return !conv.isArchived;
    }
  });

  // Sort conversations (pinned first, then by last message timestamp)
  const sortedConversations = [...filteredConversations].sort((a, b) => {
    // Pinned conversations first
    if (a.isPinned && !b.isPinned) return -1;
    if (!a.isPinned && b.isPinned) return 1;
    
    // Then by last message timestamp
    if (a.lastMessage && b.lastMessage) {
      return b.lastMessage.timestamp.getTime() - a.lastMessage.timestamp.getTime();
    }
    if (a.lastMessage && !b.lastMessage) return -1;
    if (!a.lastMessage && b.lastMessage) return 1;
    
    return 0;
  });

  const sidebarClasses = [
    'flex flex-col h-full glass-light border-r border-border-glass',
    isCollapsed ? 'w-16' : 'w-80',
    'transition-all duration-300',
    className,
  ].filter(Boolean).join(' ');

  const tabClasses = (tab: SidebarTab) => [
    'flex-1 py-2 px-4 text-sm font-medium transition-colors',
    'border-b-2 transition-colors',
    activeTab === tab
      ? 'text-primary border-primary'
      : 'text-text-secondary border-transparent hover:text-text-primary hover:border-border-glass',
  ].join(' ');

  if (isCollapsed) {
    return (
      <div className={sidebarClasses}>
        {/* Collapsed sidebar with icons only */}
        <div className="p-4 space-y-4">
          <Button
            variant="ghost"
            size="small"
            icon="message-circle"
            onClick={() => onTabChange?.('conversations')}
            className={activeTab === 'conversations' ? 'text-primary' : ''}
          />
          <Button
            variant="ghost"
            size="small"
            icon="users"
            onClick={() => onTabChange?.('contacts')}
            className={activeTab === 'contacts' ? 'text-primary' : ''}
          />
          <Button
            variant="ghost"
            size="small"
            icon="archive"
            onClick={() => onTabChange?.('archived')}
            className={activeTab === 'archived' ? 'text-primary' : ''}
          />
        </div>
        
        <div className="mt-auto p-4 space-y-2">
          <Button
            variant="ghost"
            size="small"
            icon="plus"
            onClick={onNewConversation}
          />
          <Button
            variant="ghost"
            size="small"
            icon="settings"
            onClick={onSettings}
          />
        </div>
      </div>
    );
  }

  return (
    <div className={sidebarClasses}>
      {/* Header */}
      <div className="p-4 border-b border-border-glass">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-text-primary">Messages</h2>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="small"
              icon="plus"
              onClick={onNewConversation}
              title="New conversation"
            />
            <Button
              variant="ghost"
              size="small"
              icon="settings"
              onClick={onSettings}
              title="Settings"
            />
          </div>
        </div>

        {/* Search Bar */}
        <SearchBar
          placeholder="Search conversations..."
          results={searchResults}
          loading={searchLoading}
          onSearch={handleSearch}
          onResultSelect={handleSearchResultSelect}
          showRecentSearches={true}
          recentSearches={recentSearches}
          className="mb-4"
        />

        {/* Tabs */}
        <div className="flex border-b border-border-glass">
          <button
            className={tabClasses('conversations')}
            onClick={() => onTabChange?.('conversations')}
          >
            <div className="flex items-center space-x-2">
              <Icon name="message-circle" size={16} />
              <span>Chats</span>
            </div>
          </button>
          <button
            className={tabClasses('contacts')}
            onClick={() => onTabChange?.('contacts')}
          >
            <div className="flex items-center space-x-2">
              <Icon name="users" size={16} />
              <span>Contacts</span>
            </div>
          </button>
          <button
            className={tabClasses('archived')}
            onClick={() => onTabChange?.('archived')}
          >
            <div className="flex items-center space-x-2">
              <Icon name="archive" size={16} />
              <span>Archived</span>
            </div>
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        {searchQuery ? (
          /* Search Results */
          <div className="p-4">
            <h3 className="text-sm font-medium text-text-secondary mb-3">
              Search Results
            </h3>
            {searchResults.length > 0 ? (
              <div className="space-y-1">
                {searchResults.map(result => (
                  <button
                    key={result.id}
                    onClick={() => handleSearchResultSelect(result)}
                    className="w-full p-3 text-left rounded-lg hover:bg-bg-glass-light transition-colors"
                  >
                    <div className="flex items-center space-x-3">
                      <Icon 
                        name={result.type === 'user' ? 'user' : 'message-circle'} 
                        className="text-text-secondary" 
                        size={16} 
                      />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-text-primary truncate">
                          {result.title}
                        </p>
                        {result.subtitle && (
                          <p className="text-xs text-text-secondary truncate">
                            {result.subtitle}
                          </p>
                        )}
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Icon name="search" className="mx-auto text-text-tertiary mb-2" size={24} />
                <p className="text-sm text-text-secondary">No results found</p>
              </div>
            )}
          </div>
        ) : (
          /* Conversation List */
          <div>
            {sortedConversations.length > 0 ? (
              sortedConversations.map(conversation => (
                <ConversationItem
                  key={conversation.id}
                  {...conversation}
                  isSelected={conversation.id === selectedConversationId}
                  onClick={() => handleConversationClick(conversation)}
                />
              ))
            ) : (
              <div className="p-8 text-center">
                <Icon 
                  name={activeTab === 'archived' ? 'archive' : 'message-circle'} 
                  className="mx-auto text-text-tertiary mb-3" 
                  size={32} 
                />
                <h3 className="text-sm font-medium text-text-secondary mb-1">
                  {activeTab === 'archived' ? 'No archived conversations' : 'No conversations yet'}
                </h3>
                <p className="text-xs text-text-tertiary">
                  {activeTab === 'archived' 
                    ? 'Archived conversations will appear here'
                    : 'Start a new conversation to get started'
                  }
                </p>
                {activeTab !== 'archived' && (
                  <Button
                    variant="primary"
                    size="small"
                    onClick={onNewConversation}
                    className="mt-4"
                  >
                    Start Conversation
                  </Button>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default Sidebar;
