// frontend/src/contexts/ThemeContext.tsx

import React, { createContext, useContext, useEffect, useState, type ReactNode } from 'react';
import { THEMES } from '../constants/designSystem';

type Theme = typeof THEMES.LIGHT | typeof THEMES.DARK | typeof THEMES.SYSTEM;

interface ThemeContextType {
  theme: Theme;
  effectiveTheme: typeof THEMES.LIGHT | typeof THEMES.DARK;
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [theme, setThemeState] = useState<Theme>(() => {
    // Get theme from localStorage or default to system
    const savedTheme = localStorage.getItem('theme') as Theme;
    return savedTheme || THEMES.SYSTEM;
  });

  const [effectiveTheme, setEffectiveTheme] = useState<typeof THEMES.LIGHT | typeof THEMES.DARK>(THEMES.LIGHT);

  // Function to get system theme preference
  const getSystemTheme = (): typeof THEMES.LIGHT | typeof THEMES.DARK => {
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? THEMES.DARK : THEMES.LIGHT;
  };

  // Calculate effective theme based on current theme setting
  const calculateEffectiveTheme = (currentTheme: Theme): typeof THEMES.LIGHT | typeof THEMES.DARK => {
    if (currentTheme === THEMES.SYSTEM) {
      return getSystemTheme();
    }
    return currentTheme as typeof THEMES.LIGHT | typeof THEMES.DARK;
  };

  // Update theme
  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme);
    localStorage.setItem('theme', newTheme);
  };

  // Toggle between light and dark (ignoring system)
  const toggleTheme = () => {
    const newTheme = effectiveTheme === THEMES.LIGHT ? THEMES.DARK : THEMES.LIGHT;
    setTheme(newTheme);
  };

  // Listen for system theme changes
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleSystemThemeChange = () => {
      if (theme === THEMES.SYSTEM) {
        setEffectiveTheme(getSystemTheme());
      }
    };

    mediaQuery.addEventListener('change', handleSystemThemeChange);
    return () => mediaQuery.removeEventListener('change', handleSystemThemeChange);
  }, [theme]);

  // Update effective theme when theme changes
  useEffect(() => {
    const newEffectiveTheme = calculateEffectiveTheme(theme);
    setEffectiveTheme(newEffectiveTheme);
  }, [theme]);

  // Apply theme to document
  useEffect(() => {
    const root = document.documentElement;
    
    // Remove existing theme classes
    root.removeAttribute('data-theme');
    
    // Apply new theme
    if (effectiveTheme === THEMES.DARK) {
      root.setAttribute('data-theme', 'dark');
    }
    
    // Also update the class for compatibility
    root.classList.remove('light', 'dark');
    root.classList.add(effectiveTheme);
  }, [effectiveTheme]);

  const value: ThemeContextType = {
    theme,
    effectiveTheme,
    setTheme,
    toggleTheme,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export default ThemeContext;
